package handlers

import (
	"fmt"
	"net/http"

	"github.com/eldon111/impact-resume-service/internal/config"
	"github.com/eldon111/impact-resume-service/internal/services"
	"github.com/labstack/echo/v4"
	"github.com/markbates/goth/gothic"
)

type AuthHandler struct {
	authService services.AuthServiceInterface
	authConfig  *config.AuthConfig
}

type RefreshRequest struct {
	Token string `json:"token"`
}

type RefreshResponse struct {
	Token string `json:"token"`
}

func NewAuthHandler(authService services.AuthServiceInterface, authConfig *config.AuthConfig) *AuthHandler {
	return &AuthHandler{
		authService: authService,
		authConfig:  authConfig,
	}
}

// HandleGitHubLogin handles GitHub OAuth login
// @Summary      GitHub OAuth login
// @Description  Initiate GitHub OAuth authentication flow
// @Tags         auth
// @Accept       json
// @Produce      json
// @Success      302  {string}  string  "Redirect to GitHub"
// @Router       /auth/github/login [get]
func (ah *AuthHandler) HandleGitHubLogin(c echo.Context) error {
	return ah.handleOAuthLogin(c, "github")
}

// HandleGoogleLogin handles Google OAuth login
// @Summary      Google OAuth login
// @Description  Initiate Google OAuth authentication flow
// @Tags         auth
// @Accept       json
// @Produce      json
// @Success      302  {string}  string  "Redirect to Google"
// @Router       /auth/google/login [get]
func (ah *AuthHandler) HandleGoogleLogin(c echo.Context) error {
	return ah.handleOAuthLogin(c, "google")
}

// HandleGitHubCallback handles GitHub OAuth callback
// @Summary      GitHub OAuth callback
// @Description  Handle GitHub OAuth callback and redirect to frontend with JWT token
// @Tags         auth
// @Accept       json
// @Produce      json
// @Success      302  {string}  string  "Redirect to frontend with token"
// @Failure      400  {object}  ErrorResponse
// @Failure      500  {object}  ErrorResponse
// @Router       /auth/github/callback [get]
func (ah *AuthHandler) HandleGitHubCallback(c echo.Context) error {
	return ah.handleOAuthCallback(c, "github")
}

// HandleGoogleCallback handles Google OAuth callback
// @Summary      Google OAuth callback
// @Description  Handle Google OAuth callback and redirect to frontend with JWT token
// @Tags         auth
// @Accept       json
// @Produce      json
// @Success      302  {string}  string  "Redirect to frontend with token"
// @Failure      400  {object}  ErrorResponse
// @Failure      500  {object}  ErrorResponse
// @Router       /auth/google/callback [get]
func (ah *AuthHandler) HandleGoogleCallback(c echo.Context) error {
	return ah.handleOAuthCallback(c, "google")
}

// HandleLinkedInLogin handles LinkedIn OAuth login
// @Summary      LinkedIn OAuth login
// @Description  Initiate LinkedIn OAuth authentication flow
// @Tags         auth
// @Accept       json
// @Produce      json
// @Success      302  {string}  string  "Redirect to LinkedIn"
// @Router       /auth/linkedin/login [get]
func (ah *AuthHandler) HandleLinkedInLogin(c echo.Context) error {
	return ah.handleOAuthLogin(c, "linkedin")
}

// HandleLinkedInCallback handles LinkedIn OAuth callback
// @Summary      LinkedIn OAuth callback
// @Description  Handle LinkedIn OAuth callback and redirect to frontend with JWT token
// @Tags         auth
// @Accept       json
// @Produce      json
// @Success      302  {string}  string  "Redirect to frontend with token"
// @Failure      400  {object}  ErrorResponse
// @Failure      500  {object}  ErrorResponse
// @Router       /auth/linkedin/callback [get]
func (ah *AuthHandler) HandleLinkedInCallback(c echo.Context) error {
	return ah.handleOAuthCallback(c, "linkedin")
}

func (ah *AuthHandler) handleOAuthLogin(c echo.Context, provider string) error {
	// Add provider to query parameters for Goth
	r := c.Request()
	q := r.URL.Query()
	q.Set("provider", provider)
	r.URL.RawQuery = q.Encode()

	// Start OAuth flow
	gothic.BeginAuthHandler(c.Response(), r)
	return nil
}

func (ah *AuthHandler) handleOAuthCallback(c echo.Context, provider string) error {
	// Add provider to query parameters for Goth
	r := c.Request()
	q := r.URL.Query()
	q.Set("provider", provider)
	r.URL.RawQuery = q.Encode()

	// Complete OAuth flow
	gothUser, err := gothic.CompleteUserAuth(c.Response(), r)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, fmt.Sprintf("Failed to complete auth: %v", err))
	}

	// Handle OAuth callback using service
	user, err := ah.authService.HandleOAuthCallback(c.Request().Context(), provider, gothUser)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, fmt.Sprintf("Failed to handle OAuth callback: %v", err))
	}

	// Generate auth response with JWT token
	authResponse, err := ah.authService.GenerateAuthResponse(user)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to generate auth response")
	}

	// Redirect to frontend with token and provider
	redirectURL := ah.authConfig.GetFrontendRedirectURL(provider, authResponse.Token)
	return c.Redirect(http.StatusFound, redirectURL)
}

// HandleRefreshToken handles JWT token refresh
// @Summary      Refresh JWT token
// @Description  Refresh an existing JWT token
// @Tags         auth
// @Accept       json
// @Produce      json
// @Param        request  body      RefreshRequest  true  "Refresh token request"
// @Success      200  {object}  RefreshResponse
// @Failure      400  {object}  ErrorResponse
// @Failure      401  {object}  ErrorResponse
// @Router       /auth/refresh [post]
func (ah *AuthHandler) HandleRefreshToken(c echo.Context) error {
	var req RefreshRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	newToken, err := ah.authService.RefreshUserToken(req.Token)
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Failed to refresh token")
	}

	response := RefreshResponse{
		Token: newToken,
	}

	return c.JSON(http.StatusOK, response)
}

func (ah *AuthHandler) RegisterEchoRoutes(e *echo.Echo) {
	e.GET("/auth/github/login", ah.HandleGitHubLogin)
	e.GET("/auth/github/callback", ah.HandleGitHubCallback)
	e.GET("/auth/google/login", ah.HandleGoogleLogin)
	e.GET("/auth/google/callback", ah.HandleGoogleCallback)
	e.GET("/auth/linkedin/login", ah.HandleLinkedInLogin)
	e.GET("/auth/linkedin/callback", ah.HandleLinkedInCallback)
	e.POST("/auth/refresh", ah.HandleRefreshToken)
}
