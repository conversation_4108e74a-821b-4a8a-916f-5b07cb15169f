package database

import (
	"context"
	"time"

	"github.com/eldon111/impact-resume-service/internal/errors"
	"github.com/eldon111/impact-resume-service/internal/models"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// JobRepository defines the interface for job database operations
type JobRepository interface {
	CreateJob(ctx context.Context, job *models.Job) error
	GetJobByID(ctx context.Context, userID int, id string) (*models.Job, error)
	GetJobs(ctx context.Context, userID int) ([]models.Job, error)
	UpdateJobDescription(ctx context.Context, userID int, jobID string, description string) error
	DeleteJob(ctx context.Context, userID int, jobID string) error
}

// MongoJobRepository implements JobRepository for MongoDB
type MongoJobRepository struct {
	database   *mongo.Database
	collection *mongo.Collection
}

// NewMongoJobRepository creates a new MongoDB job repository
func NewMongoJobRepository(database *mongo.Database) *MongoJobRepository {
	return &MongoJobRepository{
		database:   database,
		collection: database.Collection("jobs"),
	}
}

// CreateJob creates a new job
func (r *MongoJobRepository) CreateJob(ctx context.Context, job *models.Job) error {
	_, err := r.collection.InsertOne(ctx, job)
	if err != nil {
		return errors.NewDatabaseError("create job", err).
			WithContext("jobID", job.ID).
			WithContext("userID", job.UserID)
	}
	return nil
}

// GetJobByID retrieves a job by ID and user ID
func (r *MongoJobRepository) GetJobByID(ctx context.Context, userID int, jobID string) (*models.Job, error) {
	filter := bson.M{
		"id":     jobID,
		"userId": userID,
	}

	var job models.Job
	err := r.collection.FindOne(ctx, filter).Decode(&job)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errors.NewJobNotFoundError(jobID, userID)
		}
		return nil, errors.NewDatabaseError("get job by ID", err).
			WithContext("jobID", jobID).
			WithContext("userID", userID)
	}
	return &job, nil
}

// GetJobs retrieves all jobs for a user
func (r *MongoJobRepository) GetJobs(ctx context.Context, userID int) ([]models.Job, error) {
	filter := bson.M{"userId": userID}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, errors.NewDatabaseError("get jobs", err).
			WithContext("userID", userID)
	}
	defer cursor.Close(ctx)

	var jobs []models.Job
	if err = cursor.All(ctx, &jobs); err != nil {
		return nil, errors.NewDatabaseError("decode jobs", err).
			WithContext("userID", userID)
	}
	return jobs, nil
}

// UpdateJobDescription updates a job's description
func (r *MongoJobRepository) UpdateJobDescription(ctx context.Context, userID int, jobID string, description string) error {
	filter := bson.M{
		"id":     jobID,
		"userId": userID,
	}
	update := bson.M{
		"$set": bson.M{
			"description": description,
			"updatedAt":   time.Now(),
		},
	}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.NewDatabaseError("update job description", err).
			WithContext("jobID", jobID).
			WithContext("userID", userID)
	}
	if result.MatchedCount == 0 {
		return errors.NewJobNotFoundError(jobID, userID)
	}
	return nil
}

// DeleteJob deletes a job by ID and user ID
func (r *MongoJobRepository) DeleteJob(ctx context.Context, userID int, jobID string) error {
	filter := bson.M{
		"id":     jobID,
		"userId": userID,
	}

	result, err := r.collection.DeleteOne(ctx, filter)
	if err != nil {
		return errors.NewDatabaseError("delete job", err).
			WithContext("jobID", jobID).
			WithContext("userID", userID)
	}
	if result.DeletedCount == 0 {
		return errors.NewJobNotFoundError(jobID, userID)
	}
	return nil
}
