package database

import (
	"context"
	"fmt"

	"github.com/eldon111/impact-resume-service/internal/errors"
	"github.com/eldon111/impact-resume-service/internal/models"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// UserRepository defines the interface for user database operations
type UserRepository interface {
	// C<PERSON><PERSON><PERSON> creates a new user
	CreateUser(ctx context.Context, user *models.User) error

	// GetUserByID retrieves a user by their ID
	GetUserByID(ctx context.Context, userID int) (*models.User, error)

	// GetUserByGitHubID retrieves a user by their GitHub ID
	GetUserByGitHubID(ctx context.Context, githubID string) (*models.User, error)

	// GetUserByGoogleID retrieves a user by their Google ID
	GetUserByGoogleID(ctx context.Context, googleID string) (*models.User, error)

	// GetUserByLinkedInID retrieves a user by their LinkedIn ID
	GetUserByLinkedInID(ctx context.Context, linkedinID string) (*models.User, error)

	// UpdateUser updates an existing user
	UpdateUser(ctx context.Context, user *models.User) error

	// DeleteUser deletes a user by their ID
	DeleteUser(ctx context.Context, userID int) error

	// GenerateUserID generates a new unique user ID
	GenerateUserID(ctx context.Context) (int, error)
}

// MongoUserRepository implements UserRepository for MongoDB
type MongoUserRepository struct {
	database   *mongo.Database
	collection *mongo.Collection
}

// NewMongoUserRepository creates a new MongoDB user repository
func NewMongoUserRepository(database *mongo.Database) *MongoUserRepository {
	return &MongoUserRepository{
		database:   database,
		collection: database.Collection("users"),
	}
}

// CreateUser creates a new user
func (r *MongoUserRepository) CreateUser(ctx context.Context, user *models.User) error {
	_, err := r.collection.InsertOne(ctx, user)
	if err != nil {
		return errors.NewDatabaseError("create user", err).
			WithContext("userID", user.ID)
	}
	return nil
}

// GetUserByID retrieves a user by their ID
func (r *MongoUserRepository) GetUserByID(ctx context.Context, userID int) (*models.User, error) {
	var user models.User
	err := r.collection.FindOne(ctx, bson.M{"_id": userID}).Decode(&user)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errors.NewUserNotFoundError(userID)
		}
		return nil, errors.NewDatabaseError("get user by ID", err).
			WithContext("userID", userID)
	}
	return &user, nil
}

// GetUserByGitHubID retrieves a user by their GitHub ID
func (r *MongoUserRepository) GetUserByGitHubID(ctx context.Context, githubID string) (*models.User, error) {
	var user models.User
	err := r.collection.FindOne(ctx, bson.M{"githubOauth.githubId": githubID}).Decode(&user)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errors.NewUserNotFoundError(githubID)
		}
		return nil, errors.NewDatabaseError("get user by GitHub ID", err).
			WithContext("githubID", githubID)
	}
	return &user, nil
}

// GetUserByGoogleID retrieves a user by their Google ID
func (r *MongoUserRepository) GetUserByGoogleID(ctx context.Context, googleID string) (*models.User, error) {
	var user models.User
	err := r.collection.FindOne(ctx, bson.M{"googleOauth.googleId": googleID}).Decode(&user)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errors.NewUserNotFoundError(googleID)
		}
		return nil, errors.NewDatabaseError("get user by Google ID", err).
			WithContext("googleID", googleID)
	}
	return &user, nil
}

// GetUserByLinkedInID retrieves a user by their LinkedIn ID
func (r *MongoUserRepository) GetUserByLinkedInID(ctx context.Context, linkedinID string) (*models.User, error) {
	var user models.User
	err := r.collection.FindOne(ctx, bson.M{"linkedinOauth.linkedinId": linkedinID}).Decode(&user)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errors.NewUserNotFoundError(linkedinID)
		}
		return nil, errors.NewDatabaseError("get user by LinkedIn ID", err).
			WithContext("linkedinID", linkedinID)
	}
	return &user, nil
}

// UpdateUser updates an existing user
func (r *MongoUserRepository) UpdateUser(ctx context.Context, user *models.User) error {
	result, err := r.collection.UpdateOne(ctx,
		bson.M{"_id": user.ID},
		bson.M{"$set": user})
	if err != nil {
		return errors.NewDatabaseError("update user", err).
			WithContext("userID", user.ID)
	}
	if result.MatchedCount == 0 {
		return errors.NewUserNotFoundError(user.ID)
	}
	return nil
}

// DeleteUser deletes a user by their ID
func (r *MongoUserRepository) DeleteUser(ctx context.Context, userID int) error {
	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": userID})
	if err != nil {
		return err
	}
	if result.DeletedCount == 0 {
		return ErrUserNotFound
	}
	return nil
}

// GenerateUserID generates a new unique user ID
func (r *MongoUserRepository) GenerateUserID(ctx context.Context) (int, error) {
	// Find the highest existing user ID
	pipeline := []bson.M{
		{"$group": bson.M{
			"_id":   nil,
			"maxId": bson.M{"$max": "$_id"},
		}},
	}

	cursor, err := r.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return 0, fmt.Errorf("failed to query max user ID: %w", err)
	}
	defer cursor.Close(ctx)

	var result struct {
		MaxID int `bson:"maxId"`
	}

	if cursor.Next(ctx) {
		if err := cursor.Decode(&result); err != nil {
			return 0, fmt.Errorf("failed to decode max user ID: %w", err)
		}
		return result.MaxID + 1, nil
	}

	// No existing users, start with ID 1
	return 1, nil
}
