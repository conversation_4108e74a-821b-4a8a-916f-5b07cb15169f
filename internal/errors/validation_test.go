package errors

import (
	"reflect"
	"testing"

	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
)

func TestNewValidationErrorFromValidator(t *testing.T) {
	// Create a test struct with validation tags
	type TestStruct struct {
		Name  string `validate:"required,min=2,max=50"`
		Email string `validate:"required,email"`
		Age   int    `validate:"min=18,max=100"`
	}

	validator := validator.New()

	// Test with invalid data
	testData := TestStruct{
		Name:  "", // Required field missing
		Email: "invalid-email", // Invalid email format
		Age:   15, // Below minimum age
	}

	err := validator.Struct(testData)
	if err == nil {
		t.Fatal("Expected validation error, got nil")
	}

	validationErr := NewValidationErrorFromValidator(err)

	// Check that it's a ValidationError
	if validationErr.Code != ErrCodeValidationFailed {
		t.<PERSON>("Expected error code %s, got %s", ErrCodeValidationFailed, validationErr.Code)
	}

	// Check that we have field errors
	if len(validationErr.Fields) == 0 {
		t.Error("Expected field errors, got none")
	}

	// Check specific field errors
	nameFieldFound := false
	emailFieldFound := false
	ageFieldFound := false

	for _, field := range validationErr.Fields {
		switch field.Field {
		case "name":
			nameFieldFound = true
			if field.Tag != "required" {
				t.Errorf("Expected name field tag 'required', got '%s'", field.Tag)
			}
		case "email":
			emailFieldFound = true
			if field.Tag != "email" {
				t.Errorf("Expected email field tag 'email', got '%s'", field.Tag)
			}
		case "age":
			ageFieldFound = true
			if field.Tag != "min" {
				t.Errorf("Expected age field tag 'min', got '%s'", field.Tag)
			}
		}
	}

	if !nameFieldFound {
		t.Error("Expected name field error")
	}
	if !emailFieldFound {
		t.Error("Expected email field error")
	}
	if !ageFieldFound {
		t.Error("Expected age field error")
	}
}

func TestNewValidationErrorFromFields(t *testing.T) {
	fields := []FieldError{
		{
			Field:   "username",
			Value:   "ab",
			Tag:     "min",
			Message: "username must be at least 3 characters long",
			Param:   "3",
		},
		{
			Field:   "password",
			Value:   "",
			Tag:     "required",
			Message: "password is required",
		},
	}

	validationErr := NewValidationErrorFromFields(fields)

	if validationErr.Code != ErrCodeValidationFailed {
		t.Errorf("Expected error code %s, got %s", ErrCodeValidationFailed, validationErr.Code)
	}

	if len(validationErr.Fields) != 2 {
		t.Errorf("Expected 2 field errors, got %d", len(validationErr.Fields))
	}

	// Check first field
	if validationErr.Fields[0].Field != "username" {
		t.Errorf("Expected first field to be 'username', got '%s'", validationErr.Fields[0].Field)
	}

	// Check second field
	if validationErr.Fields[1].Field != "password" {
		t.Errorf("Expected second field to be 'password', got '%s'", validationErr.Fields[1].Field)
	}
}

func TestNewSingleFieldValidationError(t *testing.T) {
	field := "email"
	message := "email must be a valid email address"
	value := "invalid-email"

	validationErr := NewSingleFieldValidationError(field, message, value)

	if validationErr.Code != ErrCodeValidationFailed {
		t.Errorf("Expected error code %s, got %s", ErrCodeValidationFailed, validationErr.Code)
	}

	if len(validationErr.Fields) != 1 {
		t.Errorf("Expected 1 field error, got %d", len(validationErr.Fields))
	}

	fieldErr := validationErr.Fields[0]
	if fieldErr.Field != field {
		t.Errorf("Expected field '%s', got '%s'", field, fieldErr.Field)
	}

	if fieldErr.Message != message {
		t.Errorf("Expected message '%s', got '%s'", message, fieldErr.Message)
	}

	if fieldErr.Value != value {
		t.Errorf("Expected value '%s', got '%v'", value, fieldErr.Value)
	}
}

func TestValidationError_Error(t *testing.T) {
	// Test with no fields
	validationErr := &ValidationError{
		AppError: NewAppError(ErrCodeValidationFailed, "Validation failed"),
		Fields:   []FieldError{},
	}

	expected := "VALIDATION_FAILED: Validation failed"
	if got := validationErr.Error(); got != expected {
		t.Errorf("ValidationError.Error() = %v, want %v", got, expected)
	}

	// Test with fields
	validationErr.Fields = []FieldError{
		{Field: "name", Message: "name is required"},
		{Field: "email", Message: "email must be valid"},
	}

	expected = "VALIDATION_FAILED: Validation failed: name is required; email must be valid"
	if got := validationErr.Error(); got != expected {
		t.Errorf("ValidationError.Error() = %v, want %v", got, expected)
	}
}

func TestValidationError_AddField(t *testing.T) {
	validationErr := NewSingleFieldValidationError("name", "name is required", "")

	newField := FieldError{
		Field:   "email",
		Message: "email is required",
	}

	validationErr.AddField(newField)

	if len(validationErr.Fields) != 2 {
		t.Errorf("Expected 2 fields after adding, got %d", len(validationErr.Fields))
	}

	if validationErr.Fields[1].Field != "email" {
		t.Errorf("Expected second field to be 'email', got '%s'", validationErr.Fields[1].Field)
	}
}

func TestValidationError_HasField(t *testing.T) {
	validationErr := NewSingleFieldValidationError("name", "name is required", "")

	if !validationErr.HasField("name") {
		t.Error("Expected HasField('name') to return true")
	}

	if validationErr.HasField("email") {
		t.Error("Expected HasField('email') to return false")
	}
}

func TestValidationError_GetFieldError(t *testing.T) {
	validationErr := NewSingleFieldValidationError("name", "name is required", "")

	fieldErr := validationErr.GetFieldError("name")
	if fieldErr == nil {
		t.Error("Expected to get field error for 'name'")
	} else if fieldErr.Field != "name" {
		t.Errorf("Expected field 'name', got '%s'", fieldErr.Field)
	}

	fieldErr = validationErr.GetFieldError("email")
	if fieldErr != nil {
		t.Error("Expected nil for non-existent field 'email'")
	}
}

func TestValidationError_GetFieldMessages(t *testing.T) {
	fields := []FieldError{
		{Field: "name", Message: "name is required"},
		{Field: "email", Message: "email must be valid"},
	}

	validationErr := NewValidationErrorFromFields(fields)
	messages := validationErr.GetFieldMessages()

	if len(messages) != 2 {
		t.Errorf("Expected 2 messages, got %d", len(messages))
	}

	if messages[0] != "name is required" {
		t.Errorf("Expected first message 'name is required', got '%s'", messages[0])
	}

	if messages[1] != "email must be valid" {
		t.Errorf("Expected second message 'email must be valid', got '%s'", messages[1])
	}
}

func TestFormatValidationMessage(t *testing.T) {
	tests := []struct {
		name     string
		tag      string
		field    string
		param    string
		expected string
	}{
		{
			name:     "Required field",
			tag:      "required",
			field:    "Name",
			param:    "",
			expected: "name is required",
		},
		{
			name:     "Minimum length",
			tag:      "min",
			field:    "Password",
			param:    "8",
			expected: "password must be at least 8 characters long",
		},
		{
			name:     "Email validation",
			tag:      "email",
			field:    "Email",
			param:    "",
			expected: "email must be a valid email address",
		},
		{
			name:     "Unknown tag",
			tag:      "unknown",
			field:    "Field",
			param:    "",
			expected: "field is invalid",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a mock validator.FieldError
			mockFieldError := &mockFieldError{
				tag:   tt.tag,
				field: tt.field,
				param: tt.param,
			}

			if got := formatValidationMessage(mockFieldError); got != tt.expected {
				t.Errorf("formatValidationMessage() = %v, want %v", got, tt.expected)
			}
		})
	}
}

// Mock implementation of validator.FieldError for testing
type mockFieldError struct {
	tag   string
	field string
	param string
}

func (m *mockFieldError) Tag() string                   { return m.tag }
func (m *mockFieldError) ActualTag() string             { return m.tag }
func (m *mockFieldError) Namespace() string             { return "" }
func (m *mockFieldError) StructNamespace() string       { return "" }
func (m *mockFieldError) Field() string                 { return m.field }
func (m *mockFieldError) StructField() string           { return m.field }
func (m *mockFieldError) Value() interface{}            { return nil }
func (m *mockFieldError) Param() string                 { return m.param }
func (m *mockFieldError) Kind() reflect.Kind            { return reflect.String }
func (m *mockFieldError) Type() reflect.Type            { return reflect.TypeOf("") }
func (m *mockFieldError) Translate(ut.Translator) string { return "" }
func (m *mockFieldError) Error() string                 { return "" }
