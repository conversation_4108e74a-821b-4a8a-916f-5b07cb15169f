package services

import (
	"context"
	"fmt"
	"time"

	"github.com/eldon111/impact-resume-service/internal/database"
	"github.com/eldon111/impact-resume-service/internal/errors"
	"github.com/eldon111/impact-resume-service/internal/models"
)

// JobServiceInterface defines the interface for job business logic operations
type JobServiceInterface interface {
	CreateJob(ctx context.Context, userID int, request *models.JobCreateRequest) (*models.Job, error)
	GetJob(ctx context.Context, userID int, jobID string) (*models.Job, error)
	GetJobs(ctx context.Context, userID int) ([]models.Job, error)
	UpdateJobDescription(ctx context.Context, userID int, jobID string, description string) error
	DeleteJob(ctx context.Context, userID int, jobID string) error
	ValidateJobRequest(request *models.JobCreateRequest) error
}

// JobService handles job-related business logic operations
type JobService struct {
	jobRepo   database.JobRepository
	validator *ValidationService
}

// NewJobService creates a new job service instance
func NewJobService(jobRepo database.JobRepository, validator *ValidationService) *JobService {
	return &JobService{
		jobRepo:   jobRepo,
		validator: validator,
	}
}

// CreateJob creates a new job with business validation
func (js *JobService) CreateJob(ctx context.Context, userID int, request *models.JobCreateRequest) (*models.Job, error) {
	// Validate request
	if err := js.ValidateJobRequest(request); err != nil {
		return nil, err // ValidationService already returns proper error types
	}

	// Create job model
	job := &models.Job{
		ID:          js.generateJobID(),
		UserID:      userID,
		Title:       request.Title,
		Company:     request.Company,
		Location:    request.Location,
		StartDate:   request.StartDate,
		EndDate:     request.EndDate,
		IsCurrent:   request.IsCurrent,
		Description: request.Description,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Create job in repository
	err := js.jobRepo.CreateJob(ctx, job)
	if err != nil {
		return nil, err // Repository already returns proper error types
	}

	return job, nil
}

// GetJob retrieves a job by ID with business validation
func (js *JobService) GetJob(ctx context.Context, userID int, jobID string) (*models.Job, error) {
	if jobID == "" {
		return nil, errors.NewInvalidInputError("jobID", jobID).
			WithDetails("Job ID cannot be empty")
	}

	job, err := js.jobRepo.GetJobByID(ctx, userID, jobID)
	if err != nil {
		return nil, err // Repository already returns proper error types
	}

	return job, nil
}

// GetJobs retrieves all jobs for a user
func (js *JobService) GetJobs(ctx context.Context, userID int) ([]models.Job, error) {
	jobs, err := js.jobRepo.GetJobs(ctx, userID)
	if err != nil {
		return nil, err // Repository already returns proper error types
	}

	return jobs, nil
}

// UpdateJobDescription updates a job's description with validation
func (js *JobService) UpdateJobDescription(ctx context.Context, userID int, jobID string, description string) error {
	if jobID == "" {
		return errors.NewInvalidInputError("jobID", jobID).
			WithDetails("Job ID cannot be empty")
	}

	// Validate description length (max 5000 characters as per model validation)
	if len(description) > 5000 {
		return errors.NewInvalidInputError("description", description).
			WithDetails("Description cannot exceed 5000 characters")
	}

	err := js.jobRepo.UpdateJobDescription(ctx, userID, jobID, description)
	if err != nil {
		return err // Repository already returns proper error types
	}

	return nil
}

// DeleteJob deletes a job with business validation
func (js *JobService) DeleteJob(ctx context.Context, userID int, jobID string) error {
	if jobID == "" {
		return errors.NewInvalidInputError("jobID", jobID).
			WithDetails("Job ID cannot be empty")
	}

	err := js.jobRepo.DeleteJob(ctx, userID, jobID)
	if err != nil {
		return err // Repository already returns proper error types
	}

	return nil
}

// ValidateJobRequest validates a job creation request
func (js *JobService) ValidateJobRequest(request *models.JobCreateRequest) error {
	if request == nil {
		return errors.NewInvalidInputError("request", request).
			WithDetails("Request cannot be nil")
	}

	return js.validator.ValidateStruct(request)
}

// generateJobID generates a unique job ID
func (js *JobService) generateJobID() string {
	return fmt.Sprintf("job_%d", time.Now().UnixNano())
}
