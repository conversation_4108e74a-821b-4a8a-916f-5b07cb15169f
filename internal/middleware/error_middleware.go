package middleware

import (
	"errors"
	"fmt"
	"log"
	"net/http"
	"runtime/debug"
	"time"

	apperrors "github.com/eldon111/impact-resume-service/internal/errors"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

// ErrorResponse represents the standardized error response format
type ErrorResponse struct {
	Error     string                 `json:"error"`
	Code      string                 `json:"code"`
	Message   string                 `json:"message"`
	Details   string                 `json:"details,omitempty"`
	Fields    []apperrors.FieldError `json:"fields,omitempty"`
	Context   map[string]interface{} `json:"context,omitempty"`
	RequestID string                 `json:"requestId,omitempty"`
	Timestamp string                 `json:"timestamp"`
}

// ErrorMiddlewareConfig defines the configuration for error middleware
type ErrorMiddlewareConfig struct {
	// IncludeStackTrace determines whether to include stack traces in error logs
	IncludeStackTrace bool
	// IncludeContext determines whether to include error context in responses
	IncludeContext bool
	// LogLevel determines the minimum severity level to log
	LogLevel apperrors.ErrorSeverity
	// RequestIDHeader is the header name for request ID
	RequestIDHeader string
}

// DefaultErrorMiddlewareConfig returns the default configuration
func DefaultErrorMiddlewareConfig() ErrorMiddlewareConfig {
	return ErrorMiddlewareConfig{
		IncludeStackTrace: true,
		IncludeContext:    false, // Don't expose internal context by default
		LogLevel:          apperrors.SeverityWarning,
		RequestIDHeader:   "X-Request-ID",
	}
}

// ErrorMiddleware returns an Echo middleware function for centralized error handling
func ErrorMiddleware(config ...ErrorMiddlewareConfig) echo.MiddlewareFunc {
	cfg := DefaultErrorMiddlewareConfig()
	if len(config) > 0 {
		cfg = config[0]
	}

	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Add request ID if not present
			requestID := c.Request().Header.Get(cfg.RequestIDHeader)
			if requestID == "" {
				requestID = generateRequestID()
				c.Request().Header.Set(cfg.RequestIDHeader, requestID)
				c.Response().Header().Set(cfg.RequestIDHeader, requestID)
			}

			// Execute the handler
			err := next(c)
			if err != nil {
				return handleError(c, err, cfg, requestID)
			}

			return nil
		}
	}
}

// ErrorHandler is a custom error handler for Echo
func ErrorHandler(config ...ErrorMiddlewareConfig) echo.HTTPErrorHandler {
	cfg := DefaultErrorMiddlewareConfig()
	if len(config) > 0 {
		cfg = config[0]
	}

	return func(err error, c echo.Context) {
		// Get request ID
		requestID := c.Request().Header.Get(cfg.RequestIDHeader)
		if requestID == "" {
			requestID = generateRequestID()
		}

		handleError(c, err, cfg, requestID)
	}
}

// handleError processes and responds to errors
func handleError(c echo.Context, err error, cfg ErrorMiddlewareConfig, requestID string) error {
	var appErr *apperrors.AppError
	var validationErr *apperrors.ValidationError
	var echoErr *echo.HTTPError

	// Determine error type and convert to AppError if necessary
	switch {
	case errors.As(err, &validationErr):
		appErr = validationErr.AppError
	case errors.As(err, &appErr):
		// Already an AppError
	case errors.As(err, &echoErr):
		// Convert Echo HTTP error to AppError
		appErr = convertEchoError(echoErr)
	default:
		// Unknown error type, treat as internal error
		appErr = apperrors.NewInternalError("An unexpected error occurred", err)
	}

	// Log the error
	logError(c, appErr, cfg, requestID)

	// Create response
	response := createErrorResponse(appErr, validationErr, cfg, requestID)

	// Send response
	return c.JSON(appErr.HTTPStatus, response)
}

// convertEchoError converts an Echo HTTP error to an AppError
func convertEchoError(echoErr *echo.HTTPError) *apperrors.AppError {
	var code apperrors.ErrorCode
	var severity apperrors.ErrorSeverity

	// Map HTTP status codes to error codes
	switch echoErr.Code {
	case http.StatusBadRequest:
		code = apperrors.ErrCodeInvalidInput
		severity = apperrors.SeverityWarning
	case http.StatusUnauthorized:
		code = apperrors.ErrCodeUnauthorized
		severity = apperrors.SeverityWarning
	case http.StatusForbidden:
		code = apperrors.ErrCodeForbidden
		severity = apperrors.SeverityWarning
	case http.StatusNotFound:
		code = apperrors.ErrCodeUserNotFound // Generic not found
		severity = apperrors.SeverityInfo
	case http.StatusConflict:
		code = apperrors.ErrCodeUserAlreadyExists // Generic conflict
		severity = apperrors.SeverityWarning
	case http.StatusInternalServerError:
		code = apperrors.ErrCodeInternalError
		severity = apperrors.SeverityError
	default:
		code = apperrors.ErrCodeInternalError
		severity = apperrors.SeverityError
	}

	message := "An error occurred"
	if echoErr.Message != nil {
		if msg, ok := echoErr.Message.(string); ok {
			message = msg
		} else {
			message = fmt.Sprintf("%v", echoErr.Message)
		}
	}

	appErr := apperrors.NewAppErrorWithSeverity(code, message, severity)
	appErr.HTTPStatus = echoErr.Code

	return appErr
}

// createErrorResponse creates a standardized error response
func createErrorResponse(appErr *apperrors.AppError, validationErr *apperrors.ValidationError, cfg ErrorMiddlewareConfig, requestID string) ErrorResponse {
	response := ErrorResponse{
		Error:     http.StatusText(appErr.HTTPStatus),
		Code:      string(appErr.Code),
		Message:   appErr.Message,
		Details:   appErr.Details,
		RequestID: requestID,
		Timestamp: time.Now().UTC().Format(time.RFC3339),
	}

	// Add validation fields if this is a validation error
	if validationErr != nil {
		response.Fields = validationErr.Fields
	}

	// Add context if configured to do so
	if cfg.IncludeContext && appErr.Context != nil {
		response.Context = appErr.Context
	}

	return response
}

// logError logs the error with appropriate level and context
func logError(c echo.Context, appErr *apperrors.AppError, cfg ErrorMiddlewareConfig, requestID string) {
	// Check if we should log this error based on severity
	if !shouldLogError(appErr.Severity, cfg.LogLevel) {
		return
	}

	// Prepare log context
	logContext := map[string]interface{}{
		"requestId":  requestID,
		"method":     c.Request().Method,
		"path":       c.Request().URL.Path,
		"userAgent":  c.Request().UserAgent(),
		"remoteAddr": c.RealIP(),
		"errorCode":  appErr.Code,
		"severity":   appErr.Severity,
	}

	// Add error context if available
	if appErr.Context != nil {
		for k, v := range appErr.Context {
			logContext[k] = v
		}
	}

	// Create log message
	logMessage := fmt.Sprintf("[%s] %s", appErr.Severity, appErr.Error())

	// Add stack trace for critical errors or if configured
	var stackTrace string
	if cfg.IncludeStackTrace && (appErr.Severity == apperrors.SeverityCritical || appErr.Cause != nil) {
		stackTrace = string(debug.Stack())
	}

	// Log based on severity
	switch appErr.Severity {
	case apperrors.SeverityInfo:
		log.Printf("INFO: %s | Context: %+v", logMessage, logContext)
	case apperrors.SeverityWarning:
		log.Printf("WARNING: %s | Context: %+v", logMessage, logContext)
	case apperrors.SeverityError:
		log.Printf("ERROR: %s | Context: %+v", logMessage, logContext)
		if appErr.Cause != nil {
			log.Printf("ERROR: Caused by: %v", appErr.Cause)
		}
	case apperrors.SeverityCritical:
		log.Printf("CRITICAL: %s | Context: %+v", logMessage, logContext)
		if appErr.Cause != nil {
			log.Printf("CRITICAL: Caused by: %v", appErr.Cause)
		}
		if stackTrace != "" {
			log.Printf("CRITICAL: Stack trace:\n%s", stackTrace)
		}
	}
}

// shouldLogError determines if an error should be logged based on severity levels
func shouldLogError(errorSeverity, minLogLevel apperrors.ErrorSeverity) bool {
	severityLevels := map[apperrors.ErrorSeverity]int{
		apperrors.SeverityInfo:     1,
		apperrors.SeverityWarning:  2,
		apperrors.SeverityError:    3,
		apperrors.SeverityCritical: 4,
	}

	errorLevel, exists := severityLevels[errorSeverity]
	if !exists {
		return true // Log unknown severity levels
	}

	minLevel, exists := severityLevels[minLogLevel]
	if !exists {
		return true // Log if min level is unknown
	}

	return errorLevel >= minLevel
}

// generateRequestID generates a simple request ID
func generateRequestID() string {
	return fmt.Sprintf("req_%d", time.Now().UnixNano())
}

// RequestIDMiddleware adds request ID to context
func RequestIDMiddleware() echo.MiddlewareFunc {
	return middleware.RequestIDWithConfig(middleware.RequestIDConfig{
		Generator: func() string {
			return generateRequestID()
		},
	})
}
