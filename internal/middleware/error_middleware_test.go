package middleware

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/eldon111/impact-resume-service/internal/errors"
	"github.com/labstack/echo/v4"
)

func TestErrorMiddleware(t *testing.T) {
	tests := []struct {
		name           string
		handler        echo.HandlerFunc
		expectedStatus int
		expectedCode   string
	}{
		{
			name: "AppError should be handled correctly",
			handler: func(c echo.Context) error {
				return errors.NewUserNotFoundError(123)
			},
			expectedStatus: http.StatusNotFound,
			expectedCode:   string(errors.ErrCodeUserNotFound),
		},
		{
			name: "ValidationError should be handled correctly",
			handler: func(c echo.Context) error {
				return errors.NewSingleFieldValidationError("email", "email is required", "")
			},
			expectedStatus: http.StatusBadRequest,
			expectedCode:   string(errors.ErrCodeValidationFailed),
		},
		{
			name: "Echo HTTPError should be converted",
			handler: func(c echo.Context) error {
				return echo.NewHTTPError(http.StatusBadRequest, "Bad request")
			},
			expectedStatus: http.StatusBadRequest,
			expectedCode:   string(errors.ErrCodeInvalidInput),
		},
		{
			name: "Unknown error should be treated as internal error",
			handler: func(c echo.Context) error {
				return errors.New("unknown error")
			},
			expectedStatus: http.StatusInternalServerError,
			expectedCode:   string(errors.ErrCodeInternalError),
		},
		{
			name: "No error should pass through",
			handler: func(c echo.Context) error {
				return c.JSON(http.StatusOK, map[string]string{"message": "success"})
			},
			expectedStatus: http.StatusOK,
			expectedCode:   "", // No error code expected
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := echo.New()
			e.Use(ErrorMiddleware())

			req := httptest.NewRequest(http.MethodGet, "/test", nil)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			// Execute the middleware with the test handler
			middleware := ErrorMiddleware()
			err := middleware(tt.handler)(c)

			// For successful cases, no error should be returned
			if tt.expectedStatus == http.StatusOK {
				if err != nil {
					t.Errorf("Expected no error, got %v", err)
				}
				if rec.Code != http.StatusOK {
					t.Errorf("Expected status %d, got %d", http.StatusOK, rec.Code)
				}
				return
			}

			// For error cases, check the response
			if rec.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, rec.Code)
			}

			// Parse the error response
			var errorResponse ErrorResponse
			if err := json.Unmarshal(rec.Body.Bytes(), &errorResponse); err != nil {
				t.Errorf("Failed to unmarshal error response: %v", err)
			}

			if errorResponse.Code != tt.expectedCode {
				t.Errorf("Expected error code %s, got %s", tt.expectedCode, errorResponse.Code)
			}

			// Check that timestamp is set
			if errorResponse.Timestamp == "" {
				t.Error("Expected timestamp to be set")
			}

			// Check that request ID is set
			if errorResponse.RequestID == "" {
				t.Error("Expected request ID to be set")
			}
		})
	}
}

func TestErrorHandler(t *testing.T) {
	tests := []struct {
		name           string
		err            error
		expectedStatus int
		expectedCode   string
	}{
		{
			name:           "AppError should be handled correctly",
			err:            errors.NewJobNotFoundError("job_123", 456),
			expectedStatus: http.StatusNotFound,
			expectedCode:   string(errors.ErrCodeJobNotFound),
		},
		{
			name:           "ValidationError should be handled correctly",
			err:            errors.NewValidationError("Validation failed"),
			expectedStatus: http.StatusBadRequest,
			expectedCode:   string(errors.ErrCodeValidationFailed),
		},
		{
			name:           "Echo HTTPError should be converted",
			err:            echo.NewHTTPError(http.StatusUnauthorized, "Unauthorized"),
			expectedStatus: http.StatusUnauthorized,
			expectedCode:   string(errors.ErrCodeUnauthorized),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := echo.New()
			e.HTTPErrorHandler = ErrorHandler()

			req := httptest.NewRequest(http.MethodGet, "/test", nil)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			// Call the error handler directly
			e.HTTPErrorHandler(tt.err, c)

			if rec.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, rec.Code)
			}

			// Parse the error response
			var errorResponse ErrorResponse
			if err := json.Unmarshal(rec.Body.Bytes(), &errorResponse); err != nil {
				t.Errorf("Failed to unmarshal error response: %v", err)
			}

			if errorResponse.Code != tt.expectedCode {
				t.Errorf("Expected error code %s, got %s", tt.expectedCode, errorResponse.Code)
			}
		})
	}
}

func TestConvertEchoError(t *testing.T) {
	tests := []struct {
		name           string
		echoErr        *echo.HTTPError
		expectedCode   errors.ErrorCode
		expectedStatus int
	}{
		{
			name:           "Bad Request",
			echoErr:        echo.NewHTTPError(http.StatusBadRequest, "Bad request"),
			expectedCode:   errors.ErrCodeInvalidInput,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "Unauthorized",
			echoErr:        echo.NewHTTPError(http.StatusUnauthorized, "Unauthorized"),
			expectedCode:   errors.ErrCodeUnauthorized,
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "Forbidden",
			echoErr:        echo.NewHTTPError(http.StatusForbidden, "Forbidden"),
			expectedCode:   errors.ErrCodeForbidden,
			expectedStatus: http.StatusForbidden,
		},
		{
			name:           "Not Found",
			echoErr:        echo.NewHTTPError(http.StatusNotFound, "Not found"),
			expectedCode:   errors.ErrCodeUserNotFound,
			expectedStatus: http.StatusNotFound,
		},
		{
			name:           "Conflict",
			echoErr:        echo.NewHTTPError(http.StatusConflict, "Conflict"),
			expectedCode:   errors.ErrCodeUserAlreadyExists,
			expectedStatus: http.StatusConflict,
		},
		{
			name:           "Internal Server Error",
			echoErr:        echo.NewHTTPError(http.StatusInternalServerError, "Internal error"),
			expectedCode:   errors.ErrCodeInternalError,
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name:           "Unknown status code",
			echoErr:        echo.NewHTTPError(http.StatusTeapot, "I'm a teapot"),
			expectedCode:   errors.ErrCodeInternalError,
			expectedStatus: http.StatusTeapot,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			appErr := convertEchoError(tt.echoErr)

			if appErr.Code != tt.expectedCode {
				t.Errorf("Expected error code %s, got %s", tt.expectedCode, appErr.Code)
			}

			if appErr.HTTPStatus != tt.expectedStatus {
				t.Errorf("Expected HTTP status %d, got %d", tt.expectedStatus, appErr.HTTPStatus)
			}
		})
	}
}

func TestCreateErrorResponse(t *testing.T) {
	// Test with AppError
	appErr := errors.NewUserNotFoundError(123)
	appErr = appErr.WithDetails("User does not exist")

	config := DefaultErrorMiddlewareConfig()
	config.IncludeContext = true
	requestID := "test-request-123"

	response := createErrorResponse(appErr, nil, config, requestID)

	if response.Code != string(errors.ErrCodeUserNotFound) {
		t.Errorf("Expected code %s, got %s", errors.ErrCodeUserNotFound, response.Code)
	}

	if response.Message != "User not found" {
		t.Errorf("Expected message 'User not found', got '%s'", response.Message)
	}

	if response.Details != "User does not exist" {
		t.Errorf("Expected details 'User does not exist', got '%s'", response.Details)
	}

	if response.RequestID != requestID {
		t.Errorf("Expected request ID %s, got %s", requestID, response.RequestID)
	}

	if response.Context == nil {
		t.Error("Expected context to be included")
	}

	if response.Timestamp == "" {
		t.Error("Expected timestamp to be set")
	}

	// Test with ValidationError
	validationErr := errors.NewSingleFieldValidationError("email", "email is required", "")
	response = createErrorResponse(validationErr.AppError, validationErr, config, requestID)

	if len(response.Fields) != 1 {
		t.Errorf("Expected 1 field error, got %d", len(response.Fields))
	}

	if response.Fields[0].Field != "email" {
		t.Errorf("Expected field 'email', got '%s'", response.Fields[0].Field)
	}
}

func TestShouldLogError(t *testing.T) {
	tests := []struct {
		name          string
		errorSeverity errors.ErrorSeverity
		minLogLevel   errors.ErrorSeverity
		expected      bool
	}{
		{
			name:          "Info error with Info min level should log",
			errorSeverity: errors.SeverityInfo,
			minLogLevel:   errors.SeverityInfo,
			expected:      true,
		},
		{
			name:          "Info error with Warning min level should not log",
			errorSeverity: errors.SeverityInfo,
			minLogLevel:   errors.SeverityWarning,
			expected:      false,
		},
		{
			name:          "Critical error with Warning min level should log",
			errorSeverity: errors.SeverityCritical,
			minLogLevel:   errors.SeverityWarning,
			expected:      true,
		},
		{
			name:          "Warning error with Warning min level should log",
			errorSeverity: errors.SeverityWarning,
			minLogLevel:   errors.SeverityWarning,
			expected:      true,
		},
		{
			name:          "Unknown severity should log",
			errorSeverity: errors.ErrorSeverity("unknown"),
			minLogLevel:   errors.SeverityError,
			expected:      true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := shouldLogError(tt.errorSeverity, tt.minLogLevel); got != tt.expected {
				t.Errorf("shouldLogError() = %v, want %v", got, tt.expected)
			}
		})
	}
}

func TestGenerateRequestID(t *testing.T) {
	id1 := generateRequestID()
	id2 := generateRequestID()

	if id1 == id2 {
		t.Error("Expected different request IDs")
	}

	if !strings.HasPrefix(id1, "req_") {
		t.Errorf("Expected request ID to start with 'req_', got %s", id1)
	}

	if !strings.HasPrefix(id2, "req_") {
		t.Errorf("Expected request ID to start with 'req_', got %s", id2)
	}
}
