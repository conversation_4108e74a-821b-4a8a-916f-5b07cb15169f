package main

import (
	"context"
	"log"

	"github.com/eldon111/impact-resume-service/internal/config"
	"github.com/eldon111/impact-resume-service/internal/database"
	"github.com/eldon111/impact-resume-service/internal/handlers"
	"github.com/eldon111/impact-resume-service/internal/services"

	echojwt "github.com/labstack/echo-jwt/v4"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	echoSwagger "github.com/swaggo/echo-swagger"

	"github.com/markbates/goth"
	"github.com/markbates/goth/providers/github"
	"github.com/markbates/goth/providers/google"
	"github.com/markbates/goth/providers/linkedin"

	_ "github.com/eldon111/impact-resume-service/docs"
)

// @title           ImpactResume API
// @version         1.0
// @description     Resume management and processing service with OAuth authentication
// @termsOfService  http://swagger.io/terms/

// @contact.name   API Support
// @contact.url    http://www.swagger.io/support
// @contact.email  <EMAIL>

// @license.name  Apache 2.0
// @license.url   http://www.apache.org/licenses/LICENSE-2.0.html

// @host      localhost:8080
// @BasePath  /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description JWT Authorization header using the Bearer scheme. Example: "Bearer {token}"

// @security BearerAuth

func main() {
	ctx := context.Background()

	configService, err := services.NewConfigService(ctx)
	if err != nil {
		log.Fatal("Failed to initialize config service:", err)
	}

	dbConfigData, err := configService.GetDatabaseConfig()
	if err != nil {
		log.Fatal("Failed to get database config:", err)
	}

	dbConfig := config.NewDatabaseConfig(dbConfigData.URI, dbConfigData.Database)
	client, err := dbConfig.Connect(ctx)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	// Initialize repository factory
	repoFactory := database.NewMongoRepositoryFactory(client, dbConfig.Database)
	defer func() {
		if err = repoFactory.Close(ctx); err != nil {
			log.Printf("Failed to close repository factory: %v", err)
		}
	}()

	// Initialize authentication configuration
	authConfig := config.NewAuthConfig()
	authSecrets, err := configService.GetAuthSecrets(ctx)
	if err != nil {
		log.Fatal("Failed to get auth secrets:", err)
	}

	authConfig.SetFromSecrets(authSecrets)

	// Initialize Goth providers
	goth.UseProviders(
		github.New(
			authConfig.OAuth.GitHub.ClientID,
			authConfig.OAuth.GitHub.ClientSecret,
			authConfig.GetCallbackURL("github"),
			"user:email", // Request access to user's email address
		),
		google.New(
			authConfig.OAuth.Google.ClientID,
			authConfig.OAuth.Google.ClientSecret,
			authConfig.GetCallbackURL("google"),
			"profile", "email", // Request access to user's profile and email
		),
		linkedin.New(
			authConfig.OAuth.LinkedIn.ClientID,
			authConfig.OAuth.LinkedIn.ClientSecret,
			authConfig.GetCallbackURL("linkedin"),
			"profile", "email", // Request access to user's profile and email
		),
	)

	// Initialize services
	apiKey, err := configService.GetClaudeAPIKey(ctx)
	if err != nil {
		log.Fatal("Failed to get Claude API key:", err)
	}
	jwtService := services.NewJWTService(&authConfig.JWT)
	userService := services.NewUserService(repoFactory.UserRepository())
	authService := services.NewAuthService(userService, jwtService)
	validationService := services.NewValidationService()
	jobService := services.NewJobService(repoFactory.JobRepository(), validationService)
	claudeService := services.NewClaudeService(apiKey)
	aiService := services.NewAIService(claudeService)
	processingService := services.NewProcessingService(jobService, aiService, validationService)

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(authService, authConfig)
	jobHandler := handlers.NewJobHandler(jobService, processingService)

	// Create Echo instance
	e := echo.New()

	// Middleware
	e.Use(middleware.Logger())
	e.Use(middleware.Recover())
	e.Use(middleware.CORSWithConfig(middleware.CORSConfig{
		AllowOrigins: []string{
			"http://localhost:3000", // React dev server
			"http://localhost:5173", // Vite dev server
			"http://localhost:8080", // Same origin
		},
		AllowMethods: []string{echo.OPTIONS, echo.GET, echo.HEAD, echo.PUT, echo.PATCH, echo.POST, echo.DELETE},
	}))

	// JWT middleware for protected routes
	jwtMiddleware := echojwt.WithConfig(echojwt.Config{
		SigningKey:  []byte(authConfig.JWT.Secret),
		TokenLookup: "header:Authorization:Bearer ",
	})

	// Register authentication routes
	authHandler.RegisterEchoRoutes(e)

	// Protected routes
	protected := e.Group("/api/v1")
	protected.Use(jwtMiddleware)
	protected.Use(handlers.UserMiddleware())

	// Register protected handlers
	jobHandler.RegisterEchoRoutes(protected)

	// Swagger documentation
	e.GET("/swagger/*", echoSwagger.WrapHandler)

	log.Println("Starting server on :8080")
	log.Println("Endpoints available at /api/v1")
	log.Println("Authentication endpoints:")
	log.Println("  - GitHub OAuth: /auth/github/login")
	log.Println("  - Google OAuth: /auth/google/login")
	log.Println("  - LinkedIn OAuth: /auth/linkedin/login")
	log.Println("  - Token refresh: /auth/refresh")
	log.Println("Protected endpoints require Authorization: Bearer <token>")
	log.Println("Swagger documentation available at /swagger/index.html")
	log.Println("CORS enabled")

	e.Logger.Fatal(e.Start(":8080"))
}
